:host {
  display: block;
}

/* Keep component-specific overrides minimal; most styles in global styles.scss */
.news-card .title {
  line-height: 1.3;
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
}

.news-card .description {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--color-gray-600);
  line-height: 1.5;
}

/* Enhanced hover effects for news cards in chat */
.news-wrapper.compact .news-card {
  transition: all var(--transition-fast);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}

/* Search and Filter Controls */
.news-controls {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.search-container {
  margin-bottom: var(--space-4);
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  transition: border-color var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  &::placeholder {
    color: var(--color-gray-500);
  }
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  align-items: center;
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background: var(--color-white);
  cursor: pointer;
  min-width: 120px;

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }
}

.clear-filters-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    background: var(--color-gray-200);
  }
}

.no-results {
  text-align: center;
  padding: var(--space-8);
  color: var(--color-gray-600);

  p {
    margin-bottom: var(--space-4);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-select {
    min-width: auto;
  }
}
